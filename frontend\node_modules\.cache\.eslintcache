[{"C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\Sidebar.js": "3", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\ChatInterface.js": "4", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\services\\api.js": "5", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\Message.js": "7", "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\ToolsPanel.js": "8"}, {"size": 240, "mtime": 1759753413003, "results": "9", "hashOfConfig": "10"}, {"size": 3184, "mtime": 1759753331832, "results": "11", "hashOfConfig": "10"}, {"size": 1356, "mtime": 1759753195053, "results": "12", "hashOfConfig": "10"}, {"size": 8906, "mtime": 1759754727534, "results": "13", "hashOfConfig": "10"}, {"size": 1050, "mtime": 1759754663812, "results": "14", "hashOfConfig": "10"}, {"size": 1337, "mtime": 1759819052375, "results": "15", "hashOfConfig": "10"}, {"size": 1029, "mtime": 1759754755959, "results": "16", "hashOfConfig": "10"}, {"size": 2701, "mtime": 1759754689855, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8fm5mf", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\Sidebar.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\ChatInterface.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\Message.js", [], [], "C:\\Users\\<USER>\\Deep Learning\\LLM\\frontend\\src\\components\\ToolsPanel.js", [], []]